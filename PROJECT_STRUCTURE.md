# 📁 项目结构

```
autest5/
├── 📁 backend/                    # Python FastAPI 后端
│   ├── 📄 main.py                 # 主应用文件
│   ├── 📄 test_main.py            # 单元测试
│   ├── 📄 requirements.txt        # Python依赖
│   ├── 📄 Dockerfile              # 后端Docker配置
│   ├── 📄 .flake8                 # 代码风格配置
│   └── 📄 pyproject.toml          # Python项目配置
│
├── 📁 frontend/                   # Vue3 前端
│   ├── 📁 src/                    # 源代码目录
│   │   ├── 📁 api/                # API接口
│   │   │   └── 📄 index.js        # API封装
│   │   ├── 📁 router/             # 路由配置
│   │   │   └── 📄 index.js        # 路由定义
│   │   ├── 📁 views/              # 页面组件
│   │   │   ├── 📄 Home.vue        # 首页
│   │   │   ├── 📄 Users.vue       # 用户管理
│   │   │   └── 📄 Tasks.vue       # 任务管理
│   │   ├── 📄 App.vue             # 根组件
│   │   └── 📄 main.js             # 入口文件
│   ├── 📄 index.html              # HTML模板
│   ├── 📄 package.json            # Node.js依赖
│   ├── 📄 vite.config.js          # Vite配置
│   ├── 📄 Dockerfile              # 生产环境Docker配置
│   ├── 📄 Dockerfile.dev          # 开发环境Docker配置
│   ├── 📄 nginx.conf              # Nginx配置
│   ├── 📄 .eslintrc.js            # ESLint配置
│   └── 📄 .prettierrc             # Prettier配置
│
├── 📁 scripts/                    # 自动化脚本
│   ├── 📄 setup.sh                # 项目初始化脚本
│   ├── 📄 dev.sh                  # 开发环境启动脚本
│   ├── 📄 prod.sh                 # 生产环境部署脚本
│   ├── 📄 test.sh                 # 测试运行脚本
│   └── 📄 logs.sh                 # 日志查看脚本
│
├── 📁 nginx/                      # Nginx配置
│   └── 📄 nginx.conf              # 生产环境Nginx配置
│
├── 📁 jenkins/                    # Jenkins配置
│   └── 📄 README.md               # Jenkins配置指南
│
├── 📁 data/                       # 数据目录 (运行时创建)
│   └── 📄 app.db                  # SQLite数据库文件
│
├── 📁 logs/                       # 日志目录 (运行时创建)
│
├── 📁 backups/                    # 备份目录 (运行时创建)
│
├── 📄 docker-compose.yml          # 开发环境Docker Compose
├── 📄 docker-compose.prod.yml     # 生产环境Docker Compose
├── 📄 Jenkinsfile                 # Jenkins流水线配置
├── 📄 .gitignore                  # Git忽略文件
├── 📄 .env                        # 环境变量 (运行时创建)
├── 📄 README.md                   # 项目说明文档
├── 📄 QUICKSTART.md               # 快速启动指南
└── 📄 PROJECT_STRUCTURE.md        # 项目结构说明 (本文件)
```

## 📋 文件说明

### 🐍 后端文件

| 文件 | 说明 |
|------|------|
| `main.py` | FastAPI主应用，包含所有API端点和数据库模型 |
| `test_main.py` | 单元测试文件，测试所有API端点 |
| `requirements.txt` | Python依赖包列表 |
| `Dockerfile` | 后端容器化配置 |
| `.flake8` | Python代码风格检查配置 |
| `pyproject.toml` | Black、isort、pytest配置 |

### 🌐 前端文件

| 文件 | 说明 |
|------|------|
| `src/main.js` | Vue应用入口文件 |
| `src/App.vue` | 根组件，包含导航和布局 |
| `src/api/index.js` | API接口封装，包含所有后端调用 |
| `src/router/index.js` | Vue Router路由配置 |
| `src/views/Home.vue` | 首页组件，展示技术栈和系统状态 |
| `src/views/Users.vue` | 用户管理页面 |
| `src/views/Tasks.vue` | 任务管理页面 |
| `package.json` | Node.js依赖和脚本配置 |
| `vite.config.js` | Vite构建工具配置 |
| `nginx.conf` | 前端Nginx服务器配置 |

### 🔧 配置文件

| 文件 | 说明 |
|------|------|
| `docker-compose.yml` | 开发环境容器编排 |
| `docker-compose.prod.yml` | 生产环境容器编排 |
| `Jenkinsfile` | CI/CD流水线定义 |
| `.gitignore` | Git版本控制忽略规则 |
| `.env` | 环境变量配置 |

### 📜 脚本文件

| 脚本 | 功能 |
|------|------|
| `setup.sh` | 一键初始化项目环境 |
| `dev.sh` | 启动开发环境 |
| `prod.sh` | 部署生产环境 |
| `test.sh` | 运行所有测试 |
| `logs.sh` | 查看应用日志 |

### 📚 文档文件

| 文档 | 内容 |
|------|------|
| `README.md` | 项目详细说明和使用指南 |
| `QUICKSTART.md` | 快速启动指南 |
| `jenkins/README.md` | Jenkins配置详细指南 |
| `PROJECT_STRUCTURE.md` | 项目结构说明 (本文件) |

## 🔄 数据流

```
用户请求 → Nginx → 前端(Vue3) → API调用 → 后端(FastAPI) → 数据库(SQLite)
```

### 开发环境
```
浏览器:3000 → Vue Dev Server → API:8000 → FastAPI → SQLite
```

### 生产环境
```
浏览器:8080 → Nginx:80 → Frontend Container → API → Backend Container → SQLite
```

## 🚀 部署架构

### 开发环境
- 前端：Vite开发服务器 (热重载)
- 后端：Uvicorn开发服务器 (自动重启)
- 数据库：SQLite文件数据库
- 代理：Vite内置代理

### 生产环境
- 前端：Nginx静态文件服务
- 后端：Uvicorn生产服务器
- 数据库：SQLite文件数据库
- 代理：Nginx反向代理
- 容器：Docker容器化部署

## 🔧 技术栈详情

### 前端技术栈
- **Vue 3**: 渐进式JavaScript框架
- **Vite**: 快速构建工具
- **Vue Router**: 客户端路由
- **Element Plus**: UI组件库
- **Axios**: HTTP客户端
- **ESLint**: 代码检查
- **Prettier**: 代码格式化

### 后端技术栈
- **FastAPI**: 现代Python Web框架
- **SQLAlchemy**: Python ORM
- **SQLite**: 轻量级数据库
- **Pydantic**: 数据验证
- **Uvicorn**: ASGI服务器
- **Pytest**: 测试框架

### DevOps工具
- **Docker**: 容器化平台
- **Docker Compose**: 容器编排
- **Jenkins**: CI/CD平台
- **Nginx**: Web服务器/反向代理
- **Git**: 版本控制

## 📊 监控和日志

### 应用监控
- 健康检查端点：`/health`
- API文档：`/docs`
- 实时日志：Docker logs

### 性能监控
- 前端：Vite构建分析
- 后端：FastAPI内置性能监控
- 数据库：SQLite查询日志

## 🔒 安全特性

### 前端安全
- CORS配置
- CSP头设置
- XSS防护
- 输入验证

### 后端安全
- 数据验证 (Pydantic)
- SQL注入防护 (SQLAlchemy)
- 错误处理
- 健康检查

### 容器安全
- 非root用户运行
- 最小化镜像
- 安全扫描 (Trivy)
- 依赖检查

这个项目结构提供了一个完整的现代Web应用开发框架，包含了从开发到部署的完整流程。
