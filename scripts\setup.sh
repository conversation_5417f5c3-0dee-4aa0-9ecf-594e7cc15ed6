#!/bin/bash

# 项目初始化脚本

set -e

echo "🚀 开始初始化全栈项目..."

# 检查必要的工具
check_tool() {
    if ! command -v $1 &> /dev/null; then
        echo "❌ $1 未安装，请先安装 $1"
        exit 1
    else
        echo "✅ $1 已安装"
    fi
}

echo "📋 检查必要工具..."
check_tool "docker"
check_tool "docker-compose"
check_tool "node"
check_tool "npm"
check_tool "python3"
check_tool "pip3"

# 创建必要的目录
echo "📁 创建必要目录..."
mkdir -p logs
mkdir -p data
mkdir -p backups

# 设置权限
chmod +x scripts/*.sh

# 安装后端依赖
echo "🐍 安装后端依赖..."
cd backend
python3 -m venv venv
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt
cd ..

# 安装前端依赖
echo "🌐 安装前端依赖..."
cd frontend
npm install
cd ..

# 创建环境变量文件
echo "⚙️ 创建环境配置..."
if [ ! -f .env ]; then
    cat > .env << EOF
# 环境配置
ENVIRONMENT=development

# 数据库配置
DATABASE_URL=sqlite:///./data/app.db

# API配置
API_HOST=0.0.0.0
API_PORT=8000

# 前端配置
FRONTEND_PORT=3000
VITE_API_URL=http://localhost:8000

# Docker配置
COMPOSE_PROJECT_NAME=fullstack-app
EOF
    echo "✅ 环境配置文件已创建: .env"
else
    echo "⚠️ 环境配置文件已存在: .env"
fi

# 初始化数据库
echo "🗄️ 初始化数据库..."
cd backend
source venv/bin/activate
python -c "
from main import Base, engine
Base.metadata.create_all(bind=engine)
print('数据库初始化完成')
"
cd ..

echo "🎉 项目初始化完成！"
echo ""
echo "📖 使用说明："
echo "  开发环境启动: ./scripts/dev.sh"
echo "  生产环境启动: ./scripts/prod.sh"
echo "  运行测试: ./scripts/test.sh"
echo "  查看日志: ./scripts/logs.sh"
echo ""
echo "🌐 访问地址："
echo "  前端: http://localhost:3000"
echo "  后端API: http://localhost:8000"
echo "  API文档: http://localhost:8000/docs"
