# Windows PowerShell 项目初始化脚本

Write-Host "🚀 开始初始化全栈项目..." -ForegroundColor Green

# 检查必要的工具
function Test-Command {
    param($Command)
    try {
        Get-Command $Command -ErrorAction Stop | Out-Null
        Write-Host "✅ $Command 已安装" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ $Command 未安装，请先安装 $Command" -ForegroundColor Red
        return $false
    }
}

Write-Host "📋 检查必要工具..." -ForegroundColor Yellow

$tools = @("docker", "docker-compose", "node", "npm", "python")
$allToolsAvailable = $true

foreach ($tool in $tools) {
    if (-not (Test-Command $tool)) {
        $allToolsAvailable = $false
    }
}

if (-not $allToolsAvailable) {
    Write-Host "❌ 请安装缺失的工具后重试" -ForegroundColor Red
    exit 1
}

# 创建必要的目录
Write-Host "📁 创建必要目录..." -ForegroundColor Yellow
$directories = @("logs", "data", "backups")
foreach ($dir in $directories) {
    if (-not (Test-Path $dir)) {
        New-Item -ItemType Directory -Path $dir | Out-Null
        Write-Host "✅ 创建目录: $dir" -ForegroundColor Green
    }
}

# 安装后端依赖
Write-Host "🐍 安装后端依赖..." -ForegroundColor Yellow
Set-Location backend

# 创建虚拟环境
if (-not (Test-Path "venv")) {
    python -m venv venv
    Write-Host "✅ 创建Python虚拟环境" -ForegroundColor Green
}

# 激活虚拟环境并安装依赖
& "venv\Scripts\Activate.ps1"
python -m pip install --upgrade pip
pip install -r requirements.txt
Write-Host "✅ 后端依赖安装完成" -ForegroundColor Green

Set-Location ..

# 安装前端依赖
Write-Host "🌐 安装前端依赖..." -ForegroundColor Yellow
Set-Location frontend
npm install
Write-Host "✅ 前端依赖安装完成" -ForegroundColor Green
Set-Location ..

# 创建环境变量文件
Write-Host "⚙️ 创建环境配置..." -ForegroundColor Yellow
if (-not (Test-Path ".env")) {
    $envContent = @"
# 环境配置
ENVIRONMENT=development

# 数据库配置
DATABASE_URL=sqlite:///./data/app.db

# API配置
API_HOST=0.0.0.0
API_PORT=8000

# 前端配置
FRONTEND_PORT=3000
VITE_API_URL=http://localhost:8000

# Docker配置
COMPOSE_PROJECT_NAME=fullstack-app
"@
    $envContent | Out-File -FilePath ".env" -Encoding UTF8
    Write-Host "✅ 环境配置文件已创建: .env" -ForegroundColor Green
} else {
    Write-Host "⚠️ 环境配置文件已存在: .env" -ForegroundColor Yellow
}

# 初始化数据库
Write-Host "🗄️ 初始化数据库..." -ForegroundColor Yellow
Set-Location backend
& "venv\Scripts\Activate.ps1"
python -c @"
from main import Base, engine
Base.metadata.create_all(bind=engine)
print('数据库初始化完成')
"@
Set-Location ..

Write-Host "🎉 项目初始化完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📖 使用说明：" -ForegroundColor Cyan
Write-Host "  开发环境启动: .\scripts\dev.ps1" -ForegroundColor White
Write-Host "  生产环境启动: .\scripts\prod.ps1" -ForegroundColor White
Write-Host "  运行测试: .\scripts\test.ps1" -ForegroundColor White
Write-Host "  查看日志: .\scripts\logs.ps1" -ForegroundColor White
Write-Host ""
Write-Host "🌐 访问地址：" -ForegroundColor Cyan
Write-Host "  前端: http://localhost:3000" -ForegroundColor White
Write-Host "  后端API: http://localhost:8000" -ForegroundColor White
Write-Host "  API文档: http://localhost:8000/docs" -ForegroundColor White
