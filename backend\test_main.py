import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from main import app, get_db, Base

# 测试数据库配置
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 创建测试数据库表
Base.metadata.create_all(bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

client = TestClient(app)

def test_read_root():
    response = client.get("/")
    assert response.status_code == 200
    assert "message" in response.json()

def test_health_check():
    response = client.get("/health")
    assert response.status_code == 200
    assert response.json()["status"] == "healthy"

def test_create_user():
    user_data = {
        "username": "testuser",
        "email": "<EMAIL>",
        "full_name": "Test User"
    }
    response = client.post("/users/", json=user_data)
    assert response.status_code == 200
    data = response.json()
    assert data["username"] == user_data["username"]
    assert data["email"] == user_data["email"]
    assert "id" in data

def test_get_users():
    response = client.get("/users/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)

def test_create_task():
    # 首先创建一个用户
    user_data = {
        "username": "taskuser",
        "email": "<EMAIL>",
        "full_name": "Task User"
    }
    user_response = client.post("/users/", json=user_data)
    user_id = user_response.json()["id"]
    
    # 创建任务
    task_data = {
        "title": "测试任务",
        "description": "这是一个测试任务",
        "user_id": user_id
    }
    response = client.post("/tasks/", json=task_data)
    assert response.status_code == 200
    data = response.json()
    assert data["title"] == task_data["title"]
    assert data["user_id"] == user_id

def test_get_tasks():
    response = client.get("/tasks/")
    assert response.status_code == 200
    assert isinstance(response.json(), list)

def test_update_task():
    # 创建用户和任务
    user_data = {
        "username": "updateuser",
        "email": "<EMAIL>",
        "full_name": "Update User"
    }
    user_response = client.post("/users/", json=user_data)
    user_id = user_response.json()["id"]
    
    task_data = {
        "title": "原始任务",
        "description": "原始描述",
        "user_id": user_id
    }
    task_response = client.post("/tasks/", json=task_data)
    task_id = task_response.json()["id"]
    
    # 更新任务
    update_data = {
        "title": "更新后的任务",
        "completed": True
    }
    response = client.put(f"/tasks/{task_id}", json=update_data)
    assert response.status_code == 200
    data = response.json()
    assert data["title"] == update_data["title"]
    assert data["completed"] == True

def test_delete_task():
    # 创建用户和任务
    user_data = {
        "username": "deleteuser",
        "email": "<EMAIL>",
        "full_name": "Delete User"
    }
    user_response = client.post("/users/", json=user_data)
    user_id = user_response.json()["id"]
    
    task_data = {
        "title": "待删除任务",
        "user_id": user_id
    }
    task_response = client.post("/tasks/", json=task_data)
    task_id = task_response.json()["id"]
    
    # 删除任务
    response = client.delete(f"/tasks/{task_id}")
    assert response.status_code == 200
    
    # 验证任务已删除
    get_response = client.get(f"/tasks/{task_id}")
    assert get_response.status_code == 404
