# 生产环境 Docker Compose 配置
version: '3.8'

services:
  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: fullstack-backend-prod
    restart: unless-stopped
    volumes:
      - backend_data:/app/data
    environment:
      - PYTHONPATH=/app
      - DATABASE_URL=sqlite:///./data/app.db
      - ENVIRONMENT=production
    networks:
      - fullstack-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: fullstack-frontend-prod
    restart: unless-stopped
    ports:
      - "80:80"
    environment:
      - VITE_API_URL=http://backend:8000
    networks:
      - fullstack-network
    depends_on:
      - backend

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: fullstack-nginx-prod
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    networks:
      - fullstack-network
    depends_on:
      - frontend
      - backend

volumes:
  backend_data:
    driver: local

networks:
  fullstack-network:
    driver: bridge
