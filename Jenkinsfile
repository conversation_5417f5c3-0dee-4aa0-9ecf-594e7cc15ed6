pipeline {
    agent any
    
    environment {
        // Docker镜像名称
        BACKEND_IMAGE = 'fullstack-backend'
        FRONTEND_IMAGE = 'fullstack-frontend'
        
        // Docker Registry (如果使用私有仓库)
        DOCKER_REGISTRY = 'your-registry.com'
        
        // 部署环境
        DEPLOY_ENV = 'production'
    }
    
    stages {
        stage('代码检出') {
            steps {
                echo '正在检出代码...'
                checkout scm
                
                script {
                    // 获取Git提交信息
                    env.GIT_COMMIT_SHORT = sh(
                        script: 'git rev-parse --short HEAD',
                        returnStdout: true
                    ).trim()
                    
                    env.BUILD_TAG = "${env.BUILD_NUMBER}-${env.GIT_COMMIT_SHORT}"
                }
                
                echo "构建标签: ${env.BUILD_TAG}"
            }
        }
        
        stage('环境检查') {
            parallel {
                stage('检查Docker') {
                    steps {
                        sh 'docker --version'
                        sh 'docker-compose --version'
                    }
                }
                
                stage('检查Node.js') {
                    steps {
                        sh 'node --version'
                        sh 'npm --version'
                    }
                }
                
                stage('检查Python') {
                    steps {
                        sh 'python3 --version'
                        sh 'pip3 --version'
                    }
                }
            }
        }
        
        stage('安装依赖') {
            parallel {
                stage('后端依赖') {
                    steps {
                        dir('backend') {
                            echo '安装Python依赖...'
                            sh '''
                                python3 -m venv venv
                                . venv/bin/activate
                                pip install --upgrade pip
                                pip install -r requirements.txt
                            '''
                        }
                    }
                }
                
                stage('前端依赖') {
                    steps {
                        dir('frontend') {
                            echo '安装Node.js依赖...'
                            sh 'npm ci'
                        }
                    }
                }
            }
        }
        
        stage('代码质量检查') {
            parallel {
                stage('后端代码检查') {
                    steps {
                        dir('backend') {
                            echo '运行Python代码检查...'
                            sh '''
                                . venv/bin/activate
                                # 安装代码检查工具
                                pip install flake8 black isort
                                
                                # 代码格式检查
                                echo "检查代码格式..."
                                black --check .
                                
                                # 导入排序检查
                                echo "检查导入排序..."
                                isort --check-only .
                                
                                # 代码风格检查
                                echo "检查代码风格..."
                                flake8 . --max-line-length=88 --extend-ignore=E203,W503
                            '''
                        }
                    }
                }
                
                stage('前端代码检查') {
                    steps {
                        dir('frontend') {
                            echo '运行前端代码检查...'
                            sh '''
                                # ESLint检查
                                echo "运行ESLint..."
                                npm run lint
                                
                                # 代码格式检查
                                echo "检查代码格式..."
                                npm run format -- --check
                            '''
                        }
                    }
                }
            }
        }
        
        stage('运行测试') {
            parallel {
                stage('后端测试') {
                    steps {
                        dir('backend') {
                            echo '运行后端测试...'
                            sh '''
                                . venv/bin/activate
                                
                                # 运行单元测试
                                echo "运行单元测试..."
                                python -m pytest test_main.py -v --tb=short
                                
                                # 生成测试覆盖率报告
                                echo "生成覆盖率报告..."
                                pip install pytest-cov
                                python -m pytest test_main.py --cov=. --cov-report=xml --cov-report=html
                            '''
                        }
                    }
                    post {
                        always {
                            // 发布测试结果
                            publishTestResults testResultsPattern: 'backend/test-results.xml'
                            
                            // 发布覆盖率报告
                            publishCoverage adapters: [
                                coberturaAdapter('backend/coverage.xml')
                            ], sourceFileResolver: sourceFiles('STORE_LAST_BUILD')
                        }
                    }
                }
                
                stage('前端测试') {
                    steps {
                        dir('frontend') {
                            echo '运行前端测试...'
                            sh '''
                                # 运行单元测试
                                echo "运行单元测试..."
                                npm run test -- --reporter=junit --outputFile=test-results.xml
                                
                                # 运行E2E测试（如果有）
                                # npm run test:e2e
                            '''
                        }
                    }
                    post {
                        always {
                            // 发布测试结果
                            publishTestResults testResultsPattern: 'frontend/test-results.xml'
                        }
                    }
                }
            }
        }
        
        stage('构建Docker镜像') {
            parallel {
                stage('构建后端镜像') {
                    steps {
                        dir('backend') {
                            echo '构建后端Docker镜像...'
                            script {
                                def backendImage = docker.build("${BACKEND_IMAGE}:${env.BUILD_TAG}")
                                
                                // 标记为latest
                                sh "docker tag ${BACKEND_IMAGE}:${env.BUILD_TAG} ${BACKEND_IMAGE}:latest"
                                
                                // 如果使用私有仓库，推送镜像
                                // docker.withRegistry("https://${DOCKER_REGISTRY}", 'docker-registry-credentials') {
                                //     backendImage.push("${env.BUILD_TAG}")
                                //     backendImage.push("latest")
                                // }
                            }
                        }
                    }
                }
                
                stage('构建前端镜像') {
                    steps {
                        dir('frontend') {
                            echo '构建前端Docker镜像...'
                            script {
                                def frontendImage = docker.build("${FRONTEND_IMAGE}:${env.BUILD_TAG}")
                                
                                // 标记为latest
                                sh "docker tag ${FRONTEND_IMAGE}:${env.BUILD_TAG} ${FRONTEND_IMAGE}:latest"
                                
                                // 如果使用私有仓库，推送镜像
                                // docker.withRegistry("https://${DOCKER_REGISTRY}", 'docker-registry-credentials') {
                                //     frontendImage.push("${env.BUILD_TAG}")
                                //     frontendImage.push("latest")
                                // }
                            }
                        }
                    }
                }
            }
        }
        
        stage('安全扫描') {
            parallel {
                stage('镜像安全扫描') {
                    steps {
                        echo '运行Docker镜像安全扫描...'
                        script {
                            // 使用Trivy进行安全扫描
                            sh """
                                # 安装Trivy（如果未安装）
                                if ! command -v trivy &> /dev/null; then
                                    echo "安装Trivy..."
                                    wget -qO - https://aquasecurity.github.io/trivy-repo/deb/public.key | sudo apt-key add -
                                    echo deb https://aquasecurity.github.io/trivy-repo/deb generic main | sudo tee -a /etc/apt/sources.list
                                    sudo apt-get update
                                    sudo apt-get install trivy
                                fi
                                
                                # 扫描后端镜像
                                echo "扫描后端镜像..."
                                trivy image --format json --output backend-scan.json ${BACKEND_IMAGE}:${env.BUILD_TAG}
                                
                                # 扫描前端镜像
                                echo "扫描前端镜像..."
                                trivy image --format json --output frontend-scan.json ${FRONTEND_IMAGE}:${env.BUILD_TAG}
                            """
                        }
                    }
                    post {
                        always {
                            // 归档安全扫描报告
                            archiveArtifacts artifacts: '*-scan.json', fingerprint: true
                        }
                    }
                }
                
                stage('依赖安全检查') {
                    steps {
                        echo '检查依赖安全性...'
                        dir('frontend') {
                            sh 'npm audit --audit-level=high'
                        }
                        dir('backend') {
                            sh '''
                                . venv/bin/activate
                                pip install safety
                                safety check
                            '''
                        }
                    }
                }
            }
        }
        
        stage('部署到测试环境') {
            when {
                branch 'develop'
            }
            steps {
                echo '部署到测试环境...'
                sh '''
                    # 停止现有容器
                    docker-compose -f docker-compose.yml down || true
                    
                    # 启动新容器
                    docker-compose -f docker-compose.yml up -d
                    
                    # 等待服务启动
                    sleep 30
                    
                    # 健康检查
                    curl -f http://localhost:8000/health || exit 1
                    curl -f http://localhost:3000 || exit 1
                '''
            }
        }
        
        stage('部署到生产环境') {
            when {
                branch 'main'
            }
            steps {
                echo '部署到生产环境...'
                
                // 需要手动确认
                input message: '确认部署到生产环境？', ok: '部署'
                
                sh '''
                    # 备份当前数据库
                    echo "备份数据库..."
                    docker exec fullstack-backend-prod cp /app/data/app.db /app/data/app.db.backup.$(date +%Y%m%d_%H%M%S) || true
                    
                    # 停止现有容器
                    docker-compose -f docker-compose.prod.yml down
                    
                    # 启动新容器
                    docker-compose -f docker-compose.prod.yml up -d
                    
                    # 等待服务启动
                    sleep 60
                    
                    # 健康检查
                    curl -f http://localhost:8080/api/health || exit 1
                    curl -f http://localhost:8080 || exit 1
                '''
            }
        }
        
        stage('集成测试') {
            steps {
                echo '运行集成测试...'
                sh '''
                    # 等待服务完全启动
                    sleep 30
                    
                    # 运行API集成测试
                    echo "测试API端点..."
                    
                    # 测试健康检查
                    curl -f http://localhost:8000/health
                    
                    # 测试创建用户
                    curl -X POST http://localhost:8000/users/ \
                        -H "Content-Type: application/json" \
                        -d '{"username":"testuser","email":"<EMAIL>","full_name":"Test User"}'
                    
                    # 测试获取用户列表
                    curl -f http://localhost:8000/users/
                    
                    echo "集成测试通过！"
                '''
            }
        }
    }
    
    post {
        always {
            echo '清理工作空间...'
            
            // 清理Docker镜像（保留最新的几个版本）
            sh '''
                # 清理悬空镜像
                docker image prune -f
                
                # 清理旧的构建镜像（保留最新5个）
                docker images ${BACKEND_IMAGE} --format "table {{.Tag}}" | tail -n +6 | xargs -r docker rmi ${BACKEND_IMAGE}: || true
                docker images ${FRONTEND_IMAGE} --format "table {{.Tag}}" | tail -n +6 | xargs -r docker rmi ${FRONTEND_IMAGE}: || true
            '''
            
            // 归档构建产物
            archiveArtifacts artifacts: '**/dist/**', fingerprint: true, allowEmptyArchive: true
            
            // 清理工作空间
            cleanWs()
        }
        
        success {
            echo '构建成功！'
            
            // 发送成功通知
            emailext (
                subject: "构建成功: ${env.JOB_NAME} - ${env.BUILD_NUMBER}",
                body: """
                    构建成功！
                    
                    项目: ${env.JOB_NAME}
                    构建号: ${env.BUILD_NUMBER}
                    构建标签: ${env.BUILD_TAG}
                    Git提交: ${env.GIT_COMMIT_SHORT}
                    
                    查看详情: ${env.BUILD_URL}
                """,
                to: "${env.CHANGE_AUTHOR_EMAIL}"
            )
        }
        
        failure {
            echo '构建失败！'
            
            // 发送失败通知
            emailext (
                subject: "构建失败: ${env.JOB_NAME} - ${env.BUILD_NUMBER}",
                body: """
                    构建失败！
                    
                    项目: ${env.JOB_NAME}
                    构建号: ${env.BUILD_NUMBER}
                    Git提交: ${env.GIT_COMMIT_SHORT}
                    
                    查看详情: ${env.BUILD_URL}
                    查看日志: ${env.BUILD_URL}console
                """,
                to: "${env.CHANGE_AUTHOR_EMAIL}"
            )
        }
        
        unstable {
            echo '构建不稳定！'
        }
    }
}
