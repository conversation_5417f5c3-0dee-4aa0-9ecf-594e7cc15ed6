# 🚀 快速启动指南

这是一个使用现代技术栈构建的全栈Web应用，包含完整的CI/CD流水线。

## 📋 技术栈

- **前端**: Vue 3 + Vite + Element Plus
- **后端**: Python FastAPI + SQLite + SQLAlchemy
- **容器化**: Docker + Docker Compose
- **CI/CD**: Jenkins
- **代理**: Nginx

## ⚡ 快速开始

### 1. 环境要求

确保您的系统已安装以下工具：

```bash
# 检查工具版本
docker --version          # >= 20.10
docker-compose --version  # >= 2.0
node --version            # >= 18.0
npm --version             # >= 8.0
python3 --version         # >= 3.11
```

### 2. 克隆项目

```bash
git clone <your-repository-url>
cd autest5
```

### 3. 一键初始化

```bash
# 给脚本执行权限
chmod +x scripts/*.sh

# 运行初始化脚本
./scripts/setup.sh
```

### 4. 启动开发环境

```bash
# 启动开发环境
./scripts/dev.sh
```

等待服务启动完成后，访问：

- 🌐 **前端应用**: http://localhost:3000
- 🔧 **后端API**: http://localhost:8000
- 📚 **API文档**: http://localhost:8000/docs

## 🎯 主要功能

### 用户管理
- ✅ 创建用户
- ✅ 查看用户列表
- ✅ 用户详情

### 任务管理
- ✅ 创建任务
- ✅ 编辑任务
- ✅ 标记完成状态
- ✅ 删除任务
- ✅ 按用户筛选

### 系统功能
- ✅ 健康检查
- ✅ 实时API文档
- ✅ 响应式设计

## 🛠️ 开发指南

### 前端开发

```bash
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 代码检查
npm run lint

# 格式化代码
npm run format

# 运行测试
npm run test
```

### 后端开发

```bash
cd backend

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt

# 启动开发服务器
uvicorn main:app --reload

# 运行测试
pytest test_main.py -v

# 代码格式化
black .
isort .
```

## 🧪 测试

### 运行所有测试

```bash
./scripts/test.sh
```

### 单独运行测试

```bash
# 后端测试
cd backend
source venv/bin/activate
pytest test_main.py -v --cov=.

# 前端测试
cd frontend
npm run test
```

## 🚀 部署

### 开发环境部署

```bash
./scripts/dev.sh
```

### 生产环境部署

```bash
./scripts/prod.sh
```

生产环境访问地址：
- 🌐 **应用**: http://localhost:8080
- 🔧 **API**: http://localhost:8080/api

## 📊 监控和日志

### 查看日志

```bash
# 开发环境日志
./scripts/logs.sh

# 生产环境日志
./scripts/logs.sh prod
```

### 健康检查

```bash
# 检查后端健康状态
curl http://localhost:8000/health

# 检查前端状态
curl http://localhost:3000
```

## 🔧 常用命令

### Docker命令

```bash
# 查看运行中的容器
docker-compose ps

# 查看日志
docker-compose logs -f

# 重启服务
docker-compose restart

# 停止服务
docker-compose down

# 重新构建并启动
docker-compose up --build -d
```

### 数据库操作

```bash
# 进入后端容器
docker exec -it fullstack-backend-dev bash

# 查看数据库
sqlite3 data/app.db
.tables
.schema users
.schema tasks
```

## 🔄 CI/CD流水线

### Jenkins配置

1. 安装Jenkins和必要插件
2. 配置Git凭据
3. 创建Pipeline任务
4. 使用项目中的`Jenkinsfile`

详细配置请参考：[Jenkins配置指南](jenkins/README.md)

### 流水线阶段

1. **代码检出** - 从Git仓库拉取代码
2. **环境检查** - 验证工具版本
3. **安装依赖** - 安装前后端依赖
4. **代码质量检查** - ESLint, Black, Flake8
5. **运行测试** - 单元测试和集成测试
6. **构建镜像** - Docker镜像构建
7. **安全扫描** - 容器安全扫描
8. **部署** - 自动部署到目标环境

## 🐛 故障排除

### 常见问题

#### 1. 端口冲突
```bash
# 检查端口占用
netstat -tulpn | grep :3000
netstat -tulpn | grep :8000

# 停止占用端口的进程
sudo kill -9 <PID>
```

#### 2. Docker权限问题
```bash
# 添加用户到docker组
sudo usermod -aG docker $USER

# 重新登录或重启
newgrp docker
```

#### 3. 依赖安装失败
```bash
# 清理npm缓存
npm cache clean --force

# 清理pip缓存
pip cache purge

# 重新安装
./scripts/setup.sh
```

#### 4. 数据库连接问题
```bash
# 检查数据库文件
ls -la data/

# 重新初始化数据库
rm -f data/app.db
cd backend
source venv/bin/activate
python -c "from main import Base, engine; Base.metadata.create_all(bind=engine)"
```

### 获取帮助

- 📖 查看详细文档：[README.md](README.md)
- 🔧 Jenkins配置：[jenkins/README.md](jenkins/README.md)
- 🐛 提交Issue：[GitHub Issues](https://github.com/your-repo/issues)

## 📈 性能优化

### 前端优化
- 使用Vite的热重载
- 组件懒加载
- 图片压缩和CDN

### 后端优化
- 数据库索引优化
- API响应缓存
- 异步处理

### 容器优化
- 多阶段构建
- 镜像层缓存
- 资源限制

## 🔒 安全考虑

- CORS配置
- 输入验证
- SQL注入防护
- 容器安全扫描
- 依赖安全检查

## 🎉 下一步

1. 自定义业务逻辑
2. 添加用户认证
3. 集成第三方服务
4. 性能监控
5. 日志聚合

---

**祝您开发愉快！** 🚀

如有问题，请查看文档或提交Issue。
