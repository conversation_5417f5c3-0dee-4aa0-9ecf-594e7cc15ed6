# 全栈Web应用 - CI/CD项目

这是一个使用现代技术栈构建的全栈Web应用，包含完整的CI/CD流水线。

## 技术栈

### 前端
- **Vue 3** - 渐进式JavaScript框架
- **Vite** - 快速构建工具
- **Vue Router** - 路由管理
- **Axios** - HTTP客户端

### 后端
- **Python FastAPI** - 现代、快速的Web框架
- **SQLite** - 轻量级数据库
- **SQLAlchemy** - ORM
- **Pydantic** - 数据验证

### DevOps
- **Docker** - 容器化
- **Jenkins** - CI/CD流水线
- **Docker Compose** - 多容器编排

## 项目结构

```
├── frontend/          # Vue3前端应用
├── backend/           # FastAPI后端应用
├── docker-compose.yml # 开发环境配置
├── docker-compose.prod.yml # 生产环境配置
├── Jenkinsfile       # Jenkins流水线
└── README.md
```

## 快速开始

### 开发环境

1. 克隆项目
```bash
git clone <repository-url>
cd autest5
```

2. 启动开发环境
```bash
docker-compose up -d
```

3. 访问应用
- 前端: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

### 生产环境

```bash
docker-compose -f docker-compose.prod.yml up -d
```

## CI/CD流水线

Jenkins流水线包含以下阶段：
1. **代码检出** - 从Git仓库拉取代码
2. **依赖安装** - 安装前后端依赖
3. **代码检查** - 运行linting和格式检查
4. **单元测试** - 运行前后端测试
5. **构建镜像** - 构建Docker镜像
6. **部署** - 部署到目标环境

## 开发指南

### 前端开发
```bash
cd frontend
npm install
npm run dev
```

### 后端开发
```bash
cd backend
pip install -r requirements.txt
uvicorn main:app --reload
```

## API文档

后端API文档可在 http://localhost:8000/docs 查看（Swagger UI）

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request
