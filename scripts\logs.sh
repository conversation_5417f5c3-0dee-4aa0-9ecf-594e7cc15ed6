#!/bin/bash

# 日志查看脚本

echo "📋 查看应用日志..."

# 检查参数
if [ "$1" = "prod" ]; then
    COMPOSE_FILE="docker-compose.prod.yml"
    echo "🔍 查看生产环境日志..."
else
    COMPOSE_FILE="docker-compose.yml"
    echo "🔍 查看开发环境日志..."
fi

# 检查服务是否运行
if ! docker-compose -f $COMPOSE_FILE ps | grep -q "Up"; then
    echo "❌ 服务未运行"
    exit 1
fi

# 显示菜单
echo ""
echo "请选择要查看的日志："
echo "1) 所有服务"
echo "2) 后端服务"
echo "3) 前端服务"
echo "4) Nginx服务 (仅生产环境)"
echo "5) 实时跟踪所有日志"
echo ""

read -p "请输入选择 (1-5): " choice

case $choice in
    1)
        echo "📋 显示所有服务日志..."
        docker-compose -f $COMPOSE_FILE logs
        ;;
    2)
        echo "🐍 显示后端服务日志..."
        docker-compose -f $COMPOSE_FILE logs backend
        ;;
    3)
        echo "🌐 显示前端服务日志..."
        docker-compose -f $COMPOSE_FILE logs frontend
        ;;
    4)
        if [ "$1" = "prod" ]; then
            echo "🌐 显示Nginx服务日志..."
            docker-compose -f $COMPOSE_FILE logs nginx
        else
            echo "❌ Nginx服务仅在生产环境可用"
        fi
        ;;
    5)
        echo "📡 实时跟踪所有日志 (Ctrl+C 退出)..."
        docker-compose -f $COMPOSE_FILE logs -f
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac
