@echo off
echo 🚀 启动全栈应用项目...
echo.

REM 检查PowerShell是否可用
powershell -Command "Write-Host '检查PowerShell...'" >nul 2>&1
if errorlevel 1 (
    echo ❌ PowerShell不可用，请安装PowerShell
    pause
    exit /b 1
)

echo 📋 选择操作：
echo 1. 初始化项目
echo 2. 启动开发环境
echo 3. 启动生产环境
echo 4. 运行测试
echo 5. 查看日志
echo 6. 退出
echo.

set /p choice=请输入选择 (1-6): 

if "%choice%"=="1" (
    echo 🔧 初始化项目...
    powershell -ExecutionPolicy Bypass -File "scripts\setup.ps1"
) else if "%choice%"=="2" (
    echo 🚀 启动开发环境...
    powershell -ExecutionPolicy Bypass -File "scripts\dev.ps1"
) else if "%choice%"=="3" (
    echo 🚀 启动生产环境...
    powershell -ExecutionPolicy Bypass -File "scripts\prod.ps1"
) else if "%choice%"=="4" (
    echo 🧪 运行测试...
    powershell -ExecutionPolicy Bypass -File "scripts\test.ps1"
) else if "%choice%"=="5" (
    echo 📋 查看日志...
    powershell -ExecutionPolicy Bypass -File "scripts\logs.ps1"
) else if "%choice%"=="6" (
    echo 👋 再见！
    exit /b 0
) else (
    echo ❌ 无效选择
    pause
    exit /b 1
)

echo.
echo ✅ 操作完成！
pause
