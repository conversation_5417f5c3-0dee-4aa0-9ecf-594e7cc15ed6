# 🪟 Windows 用户指南

本指南专门为Windows用户提供详细的安装和使用说明。

## 📋 前置要求

### 必需软件

1. **Docker Desktop for Windows**
   - 下载地址：https://www.docker.com/products/docker-desktop/
   - 安装后启动Docker Desktop
   - 确保WSL 2后端已启用

2. **Node.js (18+)**
   - 下载地址：https://nodejs.org/
   - 选择LTS版本
   - 安装时勾选"Add to PATH"

3. **Python (3.11+)**
   - 下载地址：https://www.python.org/downloads/
   - 安装时勾选"Add Python to PATH"
   - 勾选"Install pip"

4. **Git for Windows**
   - 下载地址：https://git-scm.com/download/win
   - 安装时选择默认设置

### 可选软件

- **Visual Studio Code**: 推荐的代码编辑器
- **Windows Terminal**: 更好的终端体验
- **PowerShell 7**: 最新版本的PowerShell

## 🚀 快速开始

### 1. 检查环境

打开PowerShell，运行以下命令检查环境：

```powershell
# 检查Docker
docker --version

# 检查Node.js
node --version
npm --version

# 检查Python
python --version
pip --version

# 检查Git
git --version
```

### 2. 克隆项目

```powershell
git clone <your-repository-url>
cd autest5
```

### 3. 初始化项目

```powershell
# 运行初始化脚本
.\scripts\setup.ps1
```

初始化脚本会：
- ✅ 检查必要工具
- ✅ 创建必要目录
- ✅ 安装后端Python依赖
- ✅ 安装前端Node.js依赖
- ✅ 创建环境配置文件
- ✅ 初始化SQLite数据库

### 4. 启动开发环境

```powershell
# 启动开发环境
.\scripts\dev.ps1
```

等待服务启动完成后，访问：
- 🌐 **前端**: http://localhost:3000
- 🔧 **后端API**: http://localhost:8000
- 📚 **API文档**: http://localhost:8000/docs

## 🛠️ 开发指南

### 前端开发

```powershell
# 进入前端目录
cd frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 在新的PowerShell窗口中运行其他命令
npm run lint      # 代码检查
npm run format    # 代码格式化
npm run test      # 运行测试
npm run build     # 构建生产版本
```

### 后端开发

```powershell
# 进入后端目录
cd backend

# 激活虚拟环境
.\venv\Scripts\Activate.ps1

# 安装依赖
pip install -r requirements.txt

# 启动开发服务器
uvicorn main:app --reload

# 在新的PowerShell窗口中运行其他命令
pytest test_main.py -v    # 运行测试
black .                   # 代码格式化
flake8 .                  # 代码检查
```

## 🧪 测试

### 运行所有测试

```powershell
.\scripts\test.ps1
```

### 单独运行测试

```powershell
# 后端测试
cd backend
.\venv\Scripts\Activate.ps1
pytest test_main.py -v --cov=.

# 前端测试
cd frontend
npm run test
```

## 🚀 部署

### 开发环境

```powershell
.\scripts\dev.ps1
```

### 生产环境

```powershell
.\scripts\prod.ps1
```

## 📊 监控和日志

### 查看日志

```powershell
# 开发环境日志
.\scripts\logs.ps1

# 生产环境日志
.\scripts\logs.ps1 prod
```

### Docker命令

```powershell
# 查看运行中的容器
docker-compose ps

# 查看日志
docker-compose logs -f

# 重启服务
docker-compose restart

# 停止服务
docker-compose down

# 重新构建
docker-compose up --build -d
```

## 🐛 Windows特定问题解决

### 1. PowerShell执行策略问题

如果遇到"无法加载文件，因为在此系统上禁止运行脚本"错误：

```powershell
# 以管理员身份运行PowerShell，然后执行：
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
```

### 2. Docker Desktop问题

**问题**: Docker Desktop启动失败
**解决方案**:
1. 确保已启用Hyper-V或WSL 2
2. 重启Docker Desktop
3. 检查Windows版本是否支持

**问题**: 容器无法访问
**解决方案**:
1. 检查Windows防火墙设置
2. 确保Docker Desktop正在运行
3. 重启Docker服务

### 3. 端口占用问题

```powershell
# 检查端口占用
netstat -ano | findstr :3000
netstat -ano | findstr :8000

# 结束占用端口的进程
taskkill /PID <进程ID> /F
```

### 4. 虚拟环境问题

```powershell
# 如果虚拟环境激活失败，重新创建：
Remove-Item -Recurse -Force backend\venv
cd backend
python -m venv venv
.\venv\Scripts\Activate.ps1
pip install -r requirements.txt
```

### 5. 权限问题

```powershell
# 如果遇到权限问题，以管理员身份运行PowerShell
# 或者检查文件夹权限设置
```

### 6. 路径问题

Windows使用反斜杠`\`作为路径分隔符，在某些情况下可能需要使用正斜杠`/`或双反斜杠`\\`。

### 7. 字符编码问题

```powershell
# 设置PowerShell使用UTF-8编码
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
```

## 🔧 开发工具配置

### Visual Studio Code

推荐安装以下扩展：

**前端开发**:
- Vue Language Features (Volar)
- TypeScript Vue Plugin (Volar)
- ESLint
- Prettier
- Auto Rename Tag

**后端开发**:
- Python
- Python Docstring Generator
- autoDocstring
- Python Type Hint

**通用工具**:
- Docker
- GitLens
- Thunder Client (API测试)
- Live Server

### VS Code配置文件

创建`.vscode/settings.json`：

```json
{
  "python.defaultInterpreterPath": "./backend/venv/Scripts/python.exe",
  "python.terminal.activateEnvironment": true,
  "eslint.workingDirectories": ["frontend"],
  "prettier.configPath": "./frontend/.prettierrc",
  "files.associations": {
    "*.vue": "vue"
  },
  "emmet.includeLanguages": {
    "vue": "html"
  }
}
```

## 📝 常用命令速查

### 项目管理
```powershell
.\scripts\setup.ps1     # 初始化项目
.\scripts\dev.ps1       # 启动开发环境
.\scripts\prod.ps1      # 部署生产环境
.\scripts\test.ps1      # 运行测试
.\scripts\logs.ps1      # 查看日志
```

### Docker管理
```powershell
docker-compose ps                    # 查看服务状态
docker-compose logs -f               # 实时查看日志
docker-compose restart              # 重启服务
docker-compose down                  # 停止服务
docker-compose up --build -d        # 重新构建并启动
```

### 开发调试
```powershell
# 进入容器
docker exec -it fullstack-backend-dev powershell
docker exec -it fullstack-frontend-dev sh

# 查看容器资源使用
docker stats

# 清理Docker资源
docker system prune -f
```

## 🎯 性能优化建议

1. **启用WSL 2**: 提供更好的Docker性能
2. **SSD存储**: 将项目放在SSD上
3. **内存分配**: 为Docker Desktop分配足够内存
4. **防病毒软件**: 将项目目录添加到排除列表
5. **Windows更新**: 保持Windows系统最新

## 📞 获取帮助

如果遇到问题：

1. 查看错误日志：`.\scripts\logs.ps1`
2. 检查Docker Desktop状态
3. 重启相关服务
4. 查看项目文档
5. 提交GitHub Issue

---

**祝您在Windows上开发愉快！** 🚀
