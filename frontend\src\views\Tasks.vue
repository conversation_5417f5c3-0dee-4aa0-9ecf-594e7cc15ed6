<template>
  <div class="tasks">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>任务管理</span>
          <el-button type="primary" @click="showCreateDialog">
            <el-icon><Plus /></el-icon>
            添加任务
          </el-button>
        </div>
      </template>

      <!-- 筛选器 -->
      <div class="filters">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-select v-model="filterStatus" placeholder="筛选状态" clearable @change="fetchTasks">
              <el-option label="全部" value="" />
              <el-option label="进行中" :value="false" />
              <el-option label="已完成" :value="true" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="filterUser" placeholder="筛选用户" clearable @change="fetchTasks">
              <el-option label="全部用户" value="" />
              <el-option
                v-for="user in users"
                :key="user.id"
                :label="user.full_name"
                :value="user.id"
              />
            </el-select>
          </el-col>
        </el-row>
      </div>

      <!-- 任务列表 -->
      <el-table :data="tasks" v-loading="loading" stripe>
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column prop="title" label="任务标题" />
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="user_id" label="负责人" width="120">
          <template #default="scope">
            {{ getUserName(scope.row.user_id) }}
          </template>
        </el-table-column>
        <el-table-column prop="completed" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.completed ? 'success' : 'warning'">
              {{ scope.row.completed ? '已完成' : '进行中' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="scope">
            {{ formatTime(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button size="small" @click="editTask(scope.row)">
              编辑
            </el-button>
            <el-button
              size="small"
              :type="scope.row.completed ? 'warning' : 'success'"
              @click="toggleTaskStatus(scope.row)"
            >
              {{ scope.row.completed ? '标记未完成' : '标记完成' }}
            </el-button>
            <el-button size="small" type="danger" @click="deleteTask(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 创建/编辑任务对话框 -->
    <el-dialog v-model="taskDialogVisible" :title="isEditing ? '编辑任务' : '添加任务'" width="600px">
      <el-form :model="taskForm" :rules="taskRules" ref="taskFormRef" label-width="80px">
        <el-form-item label="任务标题" prop="title">
          <el-input v-model="taskForm.title" placeholder="请输入任务标题" />
        </el-form-item>
        <el-form-item label="任务描述" prop="description">
          <el-input
            v-model="taskForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入任务描述"
          />
        </el-form-item>
        <el-form-item label="负责人" prop="user_id">
          <el-select v-model="taskForm.user_id" placeholder="请选择负责人" style="width: 100%">
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.full_name"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="isEditing" label="状态" prop="completed">
          <el-switch
            v-model="taskForm.completed"
            active-text="已完成"
            inactive-text="进行中"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="taskDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveTask" :loading="saving">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { taskApi, userApi } from '@/api'

const tasks = ref([])
const users = ref([])
const loading = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

const filterStatus = ref('')
const filterUser = ref('')

const taskDialogVisible = ref(false)
const isEditing = ref(false)
const saving = ref(false)
const taskFormRef = ref()
const taskForm = reactive({
  id: null,
  title: '',
  description: '',
  user_id: null,
  completed: false
})

const taskRules = {
  title: [
    { required: true, message: '请输入任务标题', trigger: 'blur' }
  ],
  user_id: [
    { required: true, message: '请选择负责人', trigger: 'change' }
  ]
}

const getUserName = (userId) => {
  const user = users.value.find(u => u.id === userId)
  return user ? user.full_name : '未知用户'
}

const fetchTasks = async () => {
  loading.value = true
  try {
    const response = await taskApi.getTasks({
      skip: (currentPage.value - 1) * pageSize.value,
      limit: pageSize.value
    })
    let filteredTasks = response.data
    
    // 前端筛选（实际项目中应该在后端实现）
    if (filterStatus.value !== '') {
      filteredTasks = filteredTasks.filter(task => task.completed === filterStatus.value)
    }
    if (filterUser.value !== '') {
      filteredTasks = filteredTasks.filter(task => task.user_id === filterUser.value)
    }
    
    tasks.value = filteredTasks
    total.value = filteredTasks.length
  } catch (error) {
    console.error('获取任务列表失败:', error)
    ElMessage.error('获取任务列表失败')
  } finally {
    loading.value = false
  }
}

const fetchUsers = async () => {
  try {
    const response = await userApi.getUsers()
    users.value = response.data
  } catch (error) {
    console.error('获取用户列表失败:', error)
    ElMessage.error('获取用户列表失败')
  }
}

const showCreateDialog = () => {
  isEditing.value = false
  taskDialogVisible.value = true
  // 重置表单
  Object.assign(taskForm, {
    id: null,
    title: '',
    description: '',
    user_id: null,
    completed: false
  })
}

const editTask = (task) => {
  isEditing.value = true
  taskDialogVisible.value = true
  // 填充表单
  Object.assign(taskForm, {
    id: task.id,
    title: task.title,
    description: task.description,
    user_id: task.user_id,
    completed: task.completed
  })
}

const saveTask = async () => {
  if (!taskFormRef.value) return
  
  await taskFormRef.value.validate(async (valid) => {
    if (valid) {
      saving.value = true
      try {
        if (isEditing.value) {
          await taskApi.updateTask(taskForm.id, {
            title: taskForm.title,
            description: taskForm.description,
            completed: taskForm.completed
          })
          ElMessage.success('任务更新成功')
        } else {
          await taskApi.createTask({
            title: taskForm.title,
            description: taskForm.description,
            user_id: taskForm.user_id
          })
          ElMessage.success('任务创建成功')
        }
        taskDialogVisible.value = false
        fetchTasks()
      } catch (error) {
        console.error('保存任务失败:', error)
        ElMessage.error(error.response?.data?.detail || '保存任务失败')
      } finally {
        saving.value = false
      }
    }
  })
}

const toggleTaskStatus = async (task) => {
  try {
    await taskApi.updateTask(task.id, {
      completed: !task.completed
    })
    ElMessage.success('任务状态更新成功')
    fetchTasks()
  } catch (error) {
    console.error('更新任务状态失败:', error)
    ElMessage.error('更新任务状态失败')
  }
}

const deleteTask = async (task) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除任务"${task.title}"吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    await taskApi.deleteTask(task.id)
    ElMessage.success('任务删除成功')
    fetchTasks()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除任务失败:', error)
      ElMessage.error('删除任务失败')
    }
  }
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

const handleSizeChange = (val) => {
  pageSize.value = val
  fetchTasks()
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  fetchTasks()
}

onMounted(() => {
  fetchUsers()
  fetchTasks()
})
</script>

<style scoped>
.tasks {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filters {
  margin-bottom: 20px;
}

.pagination {
  margin-top: 20px;
  text-align: right;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
