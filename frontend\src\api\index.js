import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:8000',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 在发送请求之前做些什么
    console.log('发送请求:', config.method?.toUpperCase(), config.url)
    return config
  },
  (error) => {
    // 对请求错误做些什么
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    // 对响应数据做点什么
    console.log('响应成功:', response.status, response.config.url)
    return response
  },
  (error) => {
    // 对响应错误做点什么
    console.error('响应错误:', error.response?.status, error.config?.url)
    
    if (error.response?.status === 404) {
      console.error('资源未找到')
    } else if (error.response?.status === 500) {
      console.error('服务器内部错误')
    }
    
    return Promise.reject(error)
  }
)

// 用户相关API
export const userApi = {
  // 获取所有用户
  getUsers: (params = {}) => api.get('/users/', { params }),
  
  // 获取单个用户
  getUser: (id) => api.get(`/users/${id}`),
  
  // 创建用户
  createUser: (data) => api.post('/users/', data),
  
  // 获取用户的任务
  getUserTasks: (userId) => api.get(`/users/${userId}/tasks`),
}

// 任务相关API
export const taskApi = {
  // 获取所有任务
  getTasks: (params = {}) => api.get('/tasks/', { params }),
  
  // 获取单个任务
  getTask: (id) => api.get(`/tasks/${id}`),
  
  // 创建任务
  createTask: (data) => api.post('/tasks/', data),
  
  // 更新任务
  updateTask: (id, data) => api.put(`/tasks/${id}`, data),
  
  // 删除任务
  deleteTask: (id) => api.delete(`/tasks/${id}`),
}

// 系统相关API
export const systemApi = {
  // 健康检查
  healthCheck: () => api.get('/health'),
  
  // 获取系统信息
  getSystemInfo: () => api.get('/'),
}

export default api
