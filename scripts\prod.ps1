# Windows PowerShell 生产环境部署脚本

Write-Host "🚀 部署到生产环境..." -ForegroundColor Green

# 检查Docker是否运行
try {
    docker info | Out-Null
    Write-Host "✅ Docker正在运行" -ForegroundColor Green
}
catch {
    Write-Host "❌ Docker未运行，请先启动Docker Desktop" -ForegroundColor Red
    exit 1
}

# 确认部署
$confirmation = Read-Host "确认部署到生产环境？(y/N)"
if ($confirmation -ne "y" -and $confirmation -ne "Y") {
    Write-Host "❌ 部署已取消" -ForegroundColor Red
    exit 1
}

# 备份数据库
Write-Host "💾 备份数据库..." -ForegroundColor Yellow
$backupDir = "backups\$(Get-Date -Format 'yyyyMMdd_HHmmss')"
New-Item -ItemType Directory -Path $backupDir -Force | Out-Null

if (Test-Path "data\app.db") {
    Copy-Item "data\app.db" "$backupDir\app.db.backup"
    Write-Host "✅ 数据库备份完成: $backupDir\app.db.backup" -ForegroundColor Green
}

# 停止现有服务
Write-Host "🛑 停止现有服务..." -ForegroundColor Yellow
docker-compose -f docker-compose.prod.yml down 2>$null

# 清理旧镜像
Write-Host "🧹 清理旧镜像..." -ForegroundColor Yellow
docker image prune -f | Out-Null

# 构建并启动生产服务
Write-Host "🔨 构建并启动生产服务..." -ForegroundColor Yellow
docker-compose -f docker-compose.prod.yml up --build -d

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 生产服务启动失败，正在回滚..." -ForegroundColor Red
    
    # 恢复备份
    if (Test-Path "$backupDir\app.db.backup") {
        Copy-Item "$backupDir\app.db.backup" "data\app.db"
        Write-Host "✅ 数据库已恢复" -ForegroundColor Green
    }
    
    exit 1
}

# 等待服务启动
Write-Host "⏳ 等待服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 60

# 健康检查
Write-Host "🔍 检查服务状态..." -ForegroundColor Yellow

# 检查后端
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080/api/health" -UseBasicParsing -TimeoutSec 30
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ 后端服务正常" -ForegroundColor Green
    } else {
        throw "后端服务返回状态码: $($response.StatusCode)"
    }
}
catch {
    Write-Host "❌ 后端服务异常，正在回滚..." -ForegroundColor Red
    docker-compose -f docker-compose.prod.yml down
    
    # 恢复备份
    if (Test-Path "$backupDir\app.db.backup") {
        Copy-Item "$backupDir\app.db.backup" "data\app.db"
        Write-Host "✅ 数据库已恢复" -ForegroundColor Green
    }
    
    exit 1
}

# 检查前端
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8080" -UseBasicParsing -TimeoutSec 30
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ 前端服务正常" -ForegroundColor Green
    } else {
        throw "前端服务返回状态码: $($response.StatusCode)"
    }
}
catch {
    Write-Host "❌ 前端服务异常，正在回滚..." -ForegroundColor Red
    docker-compose -f docker-compose.prod.yml down
    
    # 恢复备份
    if (Test-Path "$backupDir\app.db.backup") {
        Copy-Item "$backupDir\app.db.backup" "data\app.db"
        Write-Host "✅ 数据库已恢复" -ForegroundColor Green
    }
    
    exit 1
}

# 运行集成测试
Write-Host "🧪 运行集成测试..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# 测试API端点
try {
    Invoke-WebRequest -Uri "http://localhost:8080/api/health" -UseBasicParsing | Out-Null
    Invoke-WebRequest -Uri "http://localhost:8080/api/users/" -UseBasicParsing | Out-Null
    Write-Host "✅ 集成测试通过" -ForegroundColor Green
}
catch {
    Write-Host "⚠️ 集成测试部分失败，但服务正在运行" -ForegroundColor Yellow
}

Write-Host "🎉 生产环境部署成功！" -ForegroundColor Green
Write-Host ""
Write-Host "🌐 访问地址：" -ForegroundColor Cyan
Write-Host "  应用: http://localhost:8080" -ForegroundColor White
Write-Host "  API: http://localhost:8080/api" -ForegroundColor White
Write-Host ""
Write-Host "📋 管理命令：" -ForegroundColor Cyan
Write-Host "  查看日志: docker-compose -f docker-compose.prod.yml logs -f" -ForegroundColor White
Write-Host "  停止服务: docker-compose -f docker-compose.prod.yml down" -ForegroundColor White
Write-Host "  重启服务: docker-compose -f docker-compose.prod.yml restart" -ForegroundColor White
Write-Host ""
Write-Host "💾 备份位置: $backupDir" -ForegroundColor Cyan
