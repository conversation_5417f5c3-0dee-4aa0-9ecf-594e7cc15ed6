#!/bin/bash

# 生产环境部署脚本

set -e

echo "🚀 部署到生产环境..."

# 检查Docker是否运行
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker未运行，请先启动Docker"
    exit 1
fi

# 确认部署
read -p "确认部署到生产环境？(y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 部署已取消"
    exit 1
fi

# 备份数据库
echo "💾 备份数据库..."
BACKUP_DIR="backups/$(date +%Y%m%d_%H%M%S)"
mkdir -p $BACKUP_DIR

if [ -f data/app.db ]; then
    cp data/app.db $BACKUP_DIR/app.db.backup
    echo "✅ 数据库备份完成: $BACKUP_DIR/app.db.backup"
fi

# 停止现有服务
echo "🛑 停止现有服务..."
docker-compose -f docker-compose.prod.yml down || true

# 清理旧镜像
echo "🧹 清理旧镜像..."
docker image prune -f

# 构建并启动生产服务
echo "🔨 构建并启动生产服务..."
docker-compose -f docker-compose.prod.yml up --build -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 60

# 健康检查
echo "🔍 检查服务状态..."

# 检查后端
if curl -f http://localhost:8080/api/health > /dev/null 2>&1; then
    echo "✅ 后端服务正常"
else
    echo "❌ 后端服务异常，正在回滚..."
    docker-compose -f docker-compose.prod.yml down
    
    # 恢复备份
    if [ -f $BACKUP_DIR/app.db.backup ]; then
        cp $BACKUP_DIR/app.db.backup data/app.db
        echo "✅ 数据库已恢复"
    fi
    
    exit 1
fi

# 检查前端
if curl -f http://localhost:8080 > /dev/null 2>&1; then
    echo "✅ 前端服务正常"
else
    echo "❌ 前端服务异常，正在回滚..."
    docker-compose -f docker-compose.prod.yml down
    
    # 恢复备份
    if [ -f $BACKUP_DIR/app.db.backup ]; then
        cp $BACKUP_DIR/app.db.backup data/app.db
        echo "✅ 数据库已恢复"
    fi
    
    exit 1
fi

# 运行集成测试
echo "🧪 运行集成测试..."
sleep 30

# 测试API端点
curl -f http://localhost:8080/api/health
curl -f http://localhost:8080/api/users/

echo "🎉 生产环境部署成功！"
echo ""
echo "🌐 访问地址："
echo "  应用: http://localhost:8080"
echo "  API: http://localhost:8080/api"
echo ""
echo "📋 管理命令："
echo "  查看日志: docker-compose -f docker-compose.prod.yml logs -f"
echo "  停止服务: docker-compose -f docker-compose.prod.yml down"
echo "  重启服务: docker-compose -f docker-compose.prod.yml restart"
echo ""
echo "💾 备份位置: $BACKUP_DIR"
