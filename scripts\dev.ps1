# Windows PowerShell 开发环境启动脚本

Write-Host "🚀 启动开发环境..." -ForegroundColor Green

# 检查Docker是否运行
try {
    docker info | Out-Null
    Write-Host "✅ Docker正在运行" -ForegroundColor Green
}
catch {
    Write-Host "❌ Docker未运行，请先启动Docker Desktop" -ForegroundColor Red
    exit 1
}

# 加载环境变量
if (Test-Path ".env") {
    Get-Content ".env" | ForEach-Object {
        if ($_ -match "^([^#][^=]+)=(.*)$") {
            [Environment]::SetEnvironmentVariable($matches[1], $matches[2], "Process")
        }
    }
}

# 停止现有容器
Write-Host "🛑 停止现有容器..." -ForegroundColor Yellow
docker-compose down 2>$null

# 构建并启动服务
Write-Host "🔨 构建并启动服务..." -ForegroundColor Yellow
docker-compose up --build -d

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 服务启动失败" -ForegroundColor Red
    exit 1
}

# 等待服务启动
Write-Host "⏳ 等待服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# 健康检查
Write-Host "🔍 检查服务状态..." -ForegroundColor Yellow

# 检查后端
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000/health" -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ 后端服务正常" -ForegroundColor Green
    } else {
        throw "后端服务返回状态码: $($response.StatusCode)"
    }
}
catch {
    Write-Host "❌ 后端服务异常: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "查看后端日志:" -ForegroundColor Yellow
    docker-compose logs backend
    exit 1
}

# 检查前端
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000" -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ 前端服务正常" -ForegroundColor Green
    } else {
        throw "前端服务返回状态码: $($response.StatusCode)"
    }
}
catch {
    Write-Host "❌ 前端服务异常: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "查看前端日志:" -ForegroundColor Yellow
    docker-compose logs frontend
    exit 1
}

Write-Host "🎉 开发环境启动成功！" -ForegroundColor Green
Write-Host ""
Write-Host "🌐 访问地址：" -ForegroundColor Cyan
Write-Host "  前端: http://localhost:3000" -ForegroundColor White
Write-Host "  后端API: http://localhost:8000" -ForegroundColor White
Write-Host "  API文档: http://localhost:8000/docs" -ForegroundColor White
Write-Host ""
Write-Host "📋 常用命令：" -ForegroundColor Cyan
Write-Host "  查看日志: docker-compose logs -f" -ForegroundColor White
Write-Host "  停止服务: docker-compose down" -ForegroundColor White
Write-Host "  重启服务: docker-compose restart" -ForegroundColor White
