<template>
  <div class="home">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="welcome-card">
          <template #header>
            <div class="card-header">
              <span>欢迎使用全栈应用</span>
              <el-button type="primary" @click="checkHealth">
                <el-icon><Refresh /></el-icon>
                检查系统状态
              </el-button>
            </div>
          </template>
          
          <div class="welcome-content">
            <h2>技术栈介绍</h2>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-card shadow="hover" class="tech-card">
                  <div class="tech-item">
                    <el-icon class="tech-icon" color="#42b883"><Monitor /></el-icon>
                    <h3>前端技术</h3>
                    <ul>
                      <li>Vue 3 - 渐进式框架</li>
                      <li>Vite - 快速构建工具</li>
                      <li>Element Plus - UI组件库</li>
                      <li>Vue Router - 路由管理</li>
                      <li>Pinia - 状态管理</li>
                    </ul>
                  </div>
                </el-card>
              </el-col>
              
              <el-col :span="8">
                <el-card shadow="hover" class="tech-card">
                  <div class="tech-item">
                    <el-icon class="tech-icon" color="#3776ab"><Server /></el-icon>
                    <h3>后端技术</h3>
                    <ul>
                      <li>Python FastAPI - 现代Web框架</li>
                      <li>SQLite - 轻量级数据库</li>
                      <li>SQLAlchemy - ORM框架</li>
                      <li>Pydantic - 数据验证</li>
                      <li>Uvicorn - ASGI服务器</li>
                    </ul>
                  </div>
                </el-card>
              </el-col>
              
              <el-col :span="8">
                <el-card shadow="hover" class="tech-card">
                  <div class="tech-item">
                    <el-icon class="tech-icon" color="#2496ed"><Setting /></el-icon>
                    <h3>DevOps</h3>
                    <ul>
                      <li>Docker - 容器化</li>
                      <li>Jenkins - CI/CD流水线</li>
                      <li>Docker Compose - 容器编排</li>
                      <li>Git - 版本控制</li>
                      <li>自动化测试</li>
                    </ul>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 系统状态 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>系统状态</span>
          </template>
          <div v-if="systemStatus">
            <el-descriptions :column="1" border>
              <el-descriptions-item label="状态">
                <el-tag :type="systemStatus.status === 'healthy' ? 'success' : 'danger'">
                  {{ systemStatus.status === 'healthy' ? '正常' : '异常' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="检查时间">
                {{ formatTime(systemStatus.timestamp) }}
              </el-descriptions-item>
            </el-descriptions>
          </div>
          <div v-else>
            <el-empty description="暂无系统状态信息" />
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <template #header>
            <span>快速操作</span>
          </template>
          <div class="quick-actions">
            <el-button type="primary" @click="$router.push('/users')">
              <el-icon><User /></el-icon>
              用户管理
            </el-button>
            <el-button type="success" @click="$router.push('/tasks')">
              <el-icon><List /></el-icon>
              任务管理
            </el-button>
            <el-button type="info" @click="openApiDocs">
              <el-icon><Document /></el-icon>
              API文档
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Monitor, Server, Setting, User, List, Document } from '@element-plus/icons-vue'
import { systemApi } from '@/api'

const systemStatus = ref(null)

const checkHealth = async () => {
  try {
    const response = await systemApi.healthCheck()
    systemStatus.value = response.data
    ElMessage.success('系统状态检查完成')
  } catch (error) {
    console.error('健康检查失败:', error)
    ElMessage.error('系统状态检查失败')
  }
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

const openApiDocs = () => {
  window.open('http://localhost:8000/docs', '_blank')
}

onMounted(() => {
  checkHealth()
})
</script>

<style scoped>
.home {
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-content h2 {
  text-align: center;
  margin-bottom: 30px;
  color: #303133;
}

.tech-card {
  height: 100%;
}

.tech-item {
  text-align: center;
}

.tech-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.tech-item h3 {
  margin: 16px 0;
  color: #303133;
}

.tech-item ul {
  text-align: left;
  padding-left: 20px;
}

.tech-item li {
  margin: 8px 0;
  color: #606266;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.quick-actions .el-button {
  justify-content: flex-start;
}
</style>
