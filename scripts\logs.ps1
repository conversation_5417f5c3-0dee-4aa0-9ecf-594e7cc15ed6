# Windows PowerShell 日志查看脚本

Write-Host "📋 查看应用日志..." -ForegroundColor Green

# 检查参数
$composeFile = "docker-compose.yml"
if ($args[0] -eq "prod") {
    $composeFile = "docker-compose.prod.yml"
    Write-Host "🔍 查看生产环境日志..." -ForegroundColor Yellow
} else {
    Write-Host "🔍 查看开发环境日志..." -ForegroundColor Yellow
}

# 检查服务是否运行
$runningServices = docker-compose -f $composeFile ps --services --filter "status=running"
if (-not $runningServices) {
    Write-Host "❌ 服务未运行" -ForegroundColor Red
    exit 1
}

# 显示菜单
Write-Host ""
Write-Host "请选择要查看的日志：" -ForegroundColor Cyan
Write-Host "1) 所有服务" -ForegroundColor White
Write-Host "2) 后端服务" -ForegroundColor White
Write-Host "3) 前端服务" -ForegroundColor White
Write-Host "4) Nginx服务 (仅生产环境)" -ForegroundColor White
Write-Host "5) 实时跟踪所有日志" -ForegroundColor White
Write-Host ""

$choice = Read-Host "请输入选择 (1-5)"

switch ($choice) {
    "1" {
        Write-Host "📋 显示所有服务日志..." -ForegroundColor Yellow
        docker-compose -f $composeFile logs
    }
    "2" {
        Write-Host "🐍 显示后端服务日志..." -ForegroundColor Yellow
        docker-compose -f $composeFile logs backend
    }
    "3" {
        Write-Host "🌐 显示前端服务日志..." -ForegroundColor Yellow
        docker-compose -f $composeFile logs frontend
    }
    "4" {
        if ($args[0] -eq "prod") {
            Write-Host "🌐 显示Nginx服务日志..." -ForegroundColor Yellow
            docker-compose -f $composeFile logs nginx
        } else {
            Write-Host "❌ Nginx服务仅在生产环境可用" -ForegroundColor Red
        }
    }
    "5" {
        Write-Host "📡 实时跟踪所有日志 (Ctrl+C 退出)..." -ForegroundColor Yellow
        docker-compose -f $composeFile logs -f
    }
    default {
        Write-Host "❌ 无效选择" -ForegroundColor Red
        exit 1
    }
}
