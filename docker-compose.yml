# 开发环境 Docker Compose 配置
version: '3.8'

services:
  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: fullstack-backend-dev
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - backend_data:/app/data
    environment:
      - PYTHONPATH=/app
      - DATABASE_URL=sqlite:///./data/app.db
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload
    networks:
      - fullstack-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: fullstack-frontend-dev
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - VITE_API_URL=http://localhost:8000
    command: npm run dev -- --host 0.0.0.0
    networks:
      - fullstack-network
    depends_on:
      - backend

volumes:
  backend_data:
    driver: local

networks:
  fullstack-network:
    driver: bridge
