# Windows PowerShell 测试脚本

Write-Host "🧪 运行项目测试..." -ForegroundColor Green

# 后端测试
Write-Host "🐍 运行后端测试..." -ForegroundColor Yellow
Set-Location backend

# 检查虚拟环境
if (-not (Test-Path "venv")) {
    Write-Host "❌ 虚拟环境不存在，请先运行 .\scripts\setup.ps1" -ForegroundColor Red
    exit 1
}

# 激活虚拟环境
& "venv\Scripts\Activate.ps1"

# 安装测试依赖
Write-Host "📦 安装测试依赖..." -ForegroundColor Yellow
pip install pytest pytest-cov pytest-asyncio httpx

# 运行测试
Write-Host "📋 运行单元测试..." -ForegroundColor Yellow
python -m pytest test_main.py -v

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 后端测试失败" -ForegroundColor Red
    Set-Location ..
    exit 1
}

# 运行覆盖率测试
Write-Host "📊 生成覆盖率报告..." -ForegroundColor Yellow
python -m pytest test_main.py --cov=. --cov-report=html --cov-report=term

Set-Location ..

# 前端测试
Write-Host "🌐 运行前端测试..." -ForegroundColor Yellow
Set-Location frontend

# 检查依赖
if (-not (Test-Path "node_modules")) {
    Write-Host "❌ 前端依赖未安装，请先运行 .\scripts\setup.ps1" -ForegroundColor Red
    Set-Location ..
    exit 1
}

# 运行测试
Write-Host "📋 运行前端测试..." -ForegroundColor Yellow
npm run test

if ($LASTEXITCODE -ne 0) {
    Write-Host "⚠️ 前端测试失败或跳过" -ForegroundColor Yellow
}

# 代码检查
Write-Host "🔍 运行代码检查..." -ForegroundColor Yellow
npm run lint

Set-Location ..

# 集成测试
Write-Host "🔗 运行集成测试..." -ForegroundColor Yellow

# 启动测试环境
Write-Host "🚀 启动测试环境..." -ForegroundColor Yellow
docker-compose -f docker-compose.yml up -d

if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 测试环境启动失败" -ForegroundColor Red
    exit 1
}

# 等待服务启动
Write-Host "⏳ 等待服务启动..." -ForegroundColor Yellow
Start-Sleep -Seconds 30

# API测试
Write-Host "🌐 测试API端点..." -ForegroundColor Yellow

try {
    # 健康检查
    $healthResponse = Invoke-WebRequest -Uri "http://localhost:8000/health" -UseBasicParsing
    Write-Host "✅ 健康检查通过" -ForegroundColor Green

    # 测试用户API
    Write-Host "👤 测试用户API..." -ForegroundColor Yellow
    $userBody = @{
        username = "testuser"
        email = "<EMAIL>"
        full_name = "Test User"
    } | ConvertTo-Json

    $userResponse = Invoke-WebRequest -Uri "http://localhost:8000/users/" -Method POST -Body $userBody -ContentType "application/json" -UseBasicParsing
    $userData = $userResponse.Content | ConvertFrom-Json
    $userId = $userData.id
    Write-Host "✅ 用户创建成功，ID: $userId" -ForegroundColor Green

    # 测试任务API
    Write-Host "📋 测试任务API..." -ForegroundColor Yellow
    $taskBody = @{
        title = "测试任务"
        description = "这是一个测试任务"
        user_id = $userId
    } | ConvertTo-Json

    $taskResponse = Invoke-WebRequest -Uri "http://localhost:8000/tasks/" -Method POST -Body $taskBody -ContentType "application/json" -UseBasicParsing
    $taskData = $taskResponse.Content | ConvertFrom-Json
    $taskId = $taskData.id
    Write-Host "✅ 任务创建成功，ID: $taskId" -ForegroundColor Green

    # 测试任务更新
    $updateBody = @{
        completed = $true
    } | ConvertTo-Json

    Invoke-WebRequest -Uri "http://localhost:8000/tasks/$taskId" -Method PUT -Body $updateBody -ContentType "application/json" -UseBasicParsing | Out-Null
    Write-Host "✅ 任务更新成功" -ForegroundColor Green

    # 测试前端
    Write-Host "🌐 测试前端..." -ForegroundColor Yellow
    $frontendResponse = Invoke-WebRequest -Uri "http://localhost:3000" -UseBasicParsing
    Write-Host "✅ 前端访问正常" -ForegroundColor Green

}
catch {
    Write-Host "❌ 集成测试失败: $($_.Exception.Message)" -ForegroundColor Red
    docker-compose -f docker-compose.yml logs
}
finally {
    # 清理测试环境
    Write-Host "🧹 清理测试环境..." -ForegroundColor Yellow
    docker-compose -f docker-compose.yml down
}

Write-Host "🎉 所有测试完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📊 测试报告：" -ForegroundColor Cyan
Write-Host "  后端覆盖率: backend\htmlcov\index.html" -ForegroundColor White
Write-Host "  前端测试: frontend\coverage\" -ForegroundColor White
