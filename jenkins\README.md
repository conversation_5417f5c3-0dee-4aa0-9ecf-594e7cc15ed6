# Jenkins CI/CD 配置指南

本文档介绍如何配置Jenkins来实现项目的持续集成和持续部署。

## 前置要求

### Jenkins服务器要求
- Jenkins 2.400+
- Docker 20.10+
- Docker Compose 2.0+
- Node.js 18+
- Python 3.11+
- Git

### 必需的Jenkins插件
```
- Pipeline
- Docker Pipeline
- Git
- Email Extension
- Blue Ocean (可选，提供更好的UI)
- Workspace Cleanup
- Build Timeout
- Timestamper
- AnsiColor
```

## 安装Jenkins插件

1. 进入Jenkins管理界面
2. 点击 "Manage Jenkins" > "Manage Plugins"
3. 在 "Available" 标签页搜索并安装上述插件
4. 重启Jenkins

## 配置Jenkins

### 1. 全局工具配置

进入 "Manage Jenkins" > "Global Tool Configuration"：

#### Git配置
- Name: `Default`
- Path to Git executable: `/usr/bin/git`

#### Node.js配置
- Name: `NodeJS-18`
- Install automatically: ✓
- Version: `NodeJS 18.x`

#### Python配置
- Name: `Python-3.11`
- Install automatically: ✓
- Version: `Python 3.11`

### 2. 系统配置

进入 "Manage Jenkins" > "Configure System"：

#### 邮件通知配置
- SMTP server: `your-smtp-server.com`
- Default user e-mail suffix: `@your-company.com`
- Use SMTP Authentication: ✓
- User Name: `<EMAIL>`
- Password: `your-app-password`
- Use SSL: ✓
- SMTP Port: `465`

#### Docker配置
确保Jenkins用户有权限访问Docker：
```bash
sudo usermod -aG docker jenkins
sudo systemctl restart jenkins
```

## 创建Pipeline任务

### 1. 新建任务
1. 点击 "New Item"
2. 输入任务名称：`fullstack-app-pipeline`
3. 选择 "Pipeline"
4. 点击 "OK"

### 2. 配置Pipeline

#### General设置
- Description: `全栈应用CI/CD流水线`
- Discard old builds: ✓
  - Days to keep builds: `30`
  - Max # of builds to keep: `50`

#### Build Triggers
- Poll SCM: ✓
  - Schedule: `H/5 * * * *` (每5分钟检查一次)
- GitHub hook trigger for GITScm polling: ✓

#### Pipeline设置
- Definition: `Pipeline script from SCM`
- SCM: `Git`
- Repository URL: `https://github.com/your-username/your-repo.git`
- Credentials: 添加Git凭据
- Branch Specifier: `*/main`
- Script Path: `Jenkinsfile`

### 3. 配置凭据

进入 "Manage Jenkins" > "Manage Credentials"：

#### Git凭据
- Kind: `Username with password`
- Username: `your-github-username`
- Password: `your-github-token`
- ID: `github-credentials`

#### Docker Registry凭据（如果使用私有仓库）
- Kind: `Username with password`
- Username: `your-registry-username`
- Password: `your-registry-password`
- ID: `docker-registry-credentials`

## 环境变量配置

在Pipeline配置中添加环境变量：

```groovy
environment {
    DOCKER_REGISTRY = 'your-registry.com'
    NOTIFICATION_EMAIL = '<EMAIL>'
    DEPLOY_SERVER = 'your-deploy-server.com'
}
```

## Webhook配置

### GitHub Webhook
1. 进入GitHub仓库设置
2. 点击 "Webhooks" > "Add webhook"
3. Payload URL: `http://your-jenkins-server/github-webhook/`
4. Content type: `application/json`
5. Events: `Just the push event`

### GitLab Webhook
1. 进入GitLab项目设置
2. 点击 "Webhooks"
3. URL: `http://your-jenkins-server/project/fullstack-app-pipeline`
4. Trigger: `Push events`

## 多环境部署配置

### 开发环境
- 分支: `develop`
- 自动部署: ✓
- 测试: 完整测试套件

### 测试环境
- 分支: `release/*`
- 手动确认: ✓
- 测试: 完整测试 + 集成测试

### 生产环境
- 分支: `main`
- 手动确认: ✓
- 测试: 完整测试 + 集成测试 + 安全扫描

## 监控和告警

### 构建状态监控
- 邮件通知: 构建失败时发送
- Slack集成: 实时状态更新
- 仪表板: Blue Ocean界面

### 日志管理
- 构建日志保留: 30天
- 错误日志聚合: ELK Stack
- 性能监控: Prometheus + Grafana

## 故障排除

### 常见问题

#### 1. Docker权限问题
```bash
# 解决方案
sudo usermod -aG docker jenkins
sudo systemctl restart jenkins
```

#### 2. Node.js版本问题
```bash
# 在Jenkinsfile中指定Node.js版本
tools {
    nodejs "NodeJS-18"
}
```

#### 3. Python虚拟环境问题
```bash
# 在Pipeline中创建虚拟环境
sh '''
    python3 -m venv venv
    . venv/bin/activate
    pip install -r requirements.txt
'''
```

#### 4. 网络连接问题
```bash
# 检查Jenkins服务器网络
curl -I https://github.com
curl -I https://registry.npmjs.org
```

### 调试技巧

1. **查看构建日志**
   - 点击构建号 > "Console Output"

2. **Pipeline步骤视图**
   - 使用Blue Ocean查看详细步骤

3. **工作空间检查**
   - 构建后检查工作空间文件

4. **环境变量调试**
   ```groovy
   sh 'printenv | sort'
   ```

## 性能优化

### 1. 并行构建
```groovy
parallel {
    stage('Backend Tests') { ... }
    stage('Frontend Tests') { ... }
}
```

### 2. 缓存优化
- Docker层缓存
- Node.js依赖缓存
- Python包缓存

### 3. 资源限制
```groovy
options {
    timeout(time: 30, unit: 'MINUTES')
    retry(3)
}
```

## 安全最佳实践

1. **凭据管理**
   - 使用Jenkins凭据存储
   - 定期轮换密钥
   - 最小权限原则

2. **代码扫描**
   - 静态代码分析
   - 依赖安全检查
   - 容器镜像扫描

3. **访问控制**
   - 基于角色的权限
   - 审计日志
   - 网络隔离

## 备份和恢复

### Jenkins配置备份
```bash
# 备份Jenkins配置
tar -czf jenkins-backup-$(date +%Y%m%d).tar.gz /var/lib/jenkins/
```

### 数据库备份
```bash
# 在Pipeline中自动备份
sh '''
    docker exec backend cp /app/data/app.db /app/data/backup/app.db.$(date +%Y%m%d_%H%M%S)
'''
```

## 扩展功能

### 1. 多分支Pipeline
- 自动发现分支
- 分支特定配置
- Pull Request构建

### 2. 蓝绿部署
- 零停机部署
- 快速回滚
- 流量切换

### 3. 金丝雀发布
- 渐进式部署
- 实时监控
- 自动回滚

这个配置指南提供了完整的Jenkins CI/CD设置，确保项目能够实现自动化的构建、测试和部署流程。
