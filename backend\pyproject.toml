[tool.black]
line-length = 88
target-version = ['py311']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | venv
  | _build
  | buck-out
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["app"]

[tool.pytest.ini_options]
testpaths = [
    ".",
]
python_files = [
    "test_*.py",
    "*_test.py",
]
addopts = "-v --tb=short"
filterwarnings = [
    "ignore::DeprecationWarning",
]
