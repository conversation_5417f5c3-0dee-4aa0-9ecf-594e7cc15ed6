#!/bin/bash

# 测试脚本

set -e

echo "🧪 运行项目测试..."

# 后端测试
echo "🐍 运行后端测试..."
cd backend

# 激活虚拟环境
if [ -d "venv" ]; then
    source venv/bin/activate
else
    echo "❌ 虚拟环境不存在，请先运行 ./scripts/setup.sh"
    exit 1
fi

# 安装测试依赖
pip install pytest pytest-cov pytest-asyncio httpx

# 运行测试
echo "📋 运行单元测试..."
python -m pytest test_main.py -v

# 运行覆盖率测试
echo "📊 生成覆盖率报告..."
python -m pytest test_main.py --cov=. --cov-report=html --cov-report=term

cd ..

# 前端测试
echo "🌐 运行前端测试..."
cd frontend

# 检查依赖
if [ ! -d "node_modules" ]; then
    echo "❌ 前端依赖未安装，请先运行 ./scripts/setup.sh"
    exit 1
fi

# 运行测试
echo "📋 运行前端测试..."
npm run test

# 代码检查
echo "🔍 运行代码检查..."
npm run lint

cd ..

# 集成测试
echo "🔗 运行集成测试..."

# 启动测试环境
echo "🚀 启动测试环境..."
docker-compose -f docker-compose.yml up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# API测试
echo "🌐 测试API端点..."

# 健康检查
curl -f http://localhost:8000/health

# 测试用户API
echo "👤 测试用户API..."
USER_RESPONSE=$(curl -s -X POST http://localhost:8000/users/ \
    -H "Content-Type: application/json" \
    -d '{"username":"testuser","email":"<EMAIL>","full_name":"Test User"}')

USER_ID=$(echo $USER_RESPONSE | python3 -c "import sys, json; print(json.load(sys.stdin)['id'])")
echo "✅ 用户创建成功，ID: $USER_ID"

# 测试任务API
echo "📋 测试任务API..."
TASK_RESPONSE=$(curl -s -X POST http://localhost:8000/tasks/ \
    -H "Content-Type: application/json" \
    -d "{\"title\":\"测试任务\",\"description\":\"这是一个测试任务\",\"user_id\":$USER_ID}")

TASK_ID=$(echo $TASK_RESPONSE | python3 -c "import sys, json; print(json.load(sys.stdin)['id'])")
echo "✅ 任务创建成功，ID: $TASK_ID"

# 测试任务更新
curl -s -X PUT http://localhost:8000/tasks/$TASK_ID \
    -H "Content-Type: application/json" \
    -d '{"completed":true}' > /dev/null
echo "✅ 任务更新成功"

# 测试前端
echo "🌐 测试前端..."
curl -f http://localhost:3000 > /dev/null
echo "✅ 前端访问正常"

# 清理测试环境
echo "🧹 清理测试环境..."
docker-compose -f docker-compose.yml down

echo "🎉 所有测试通过！"
echo ""
echo "📊 测试报告："
echo "  后端覆盖率: backend/htmlcov/index.html"
echo "  前端测试: frontend/coverage/"
